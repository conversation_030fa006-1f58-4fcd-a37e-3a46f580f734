<template>
  <div>
    <Form @submit="onSubmit" class="flex flex-col gap-4">
      <FormSummaryMessage />
      <FormField>
        <FormLabel>{{ t('UPLOAD_FILE') }}</FormLabel>
        <FormMessage />
      </FormField>
      <FormField>
        <FormLabel>{{ t('UPLOAD_FILE') }}</FormLabel>
        <FormMessage />
      </FormField>
    </Form>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import FormSummaryMessage from '@/components/ui/form/FormSummaryMessage.vue'
import * as z from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'

const { t } = useI18n()

const schema = z.object({
  file: z.any(),
})

const form = useForm({
  validationSchema: toTypedSchema(schema),
  name: 'upload-form',
})

const file = ref(null)

const onSubmit = (values: any) => {
  console.log(values)
}
</script>
