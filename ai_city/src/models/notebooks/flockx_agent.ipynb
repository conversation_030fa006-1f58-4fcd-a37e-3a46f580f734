{"cells": [{"cell_type": "markdown", "id": "fdf6e297", "metadata": {}, "source": ["### Flockx API not available >:("]}, {"cell_type": "code", "execution_count": 5, "id": "91277bc5", "metadata": {"ExecuteTime": {"end_time": "2025-05-24T21:46:25.049849Z", "start_time": "2025-05-24T21:46:25.046183Z"}}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 6, "id": "21614f14", "metadata": {"ExecuteTime": {"end_time": "2025-05-24T21:46:26.134844Z", "start_time": "2025-05-24T21:46:26.130046Z"}}, "outputs": [], "source": ["load_dotenv()\n", "\n", "FLOCKX_KEY = os.getenv('FLOCKX_KEY')\n", "AGENT_ID = \"af1d4156-2b7b-45cc-98d2-ee8f96652c56\""]}, {"cell_type": "code", "execution_count": null, "id": "c7140a96", "metadata": {"ExecuteTime": {"end_time": "2025-05-24T21:46:27.203426Z", "start_time": "2025-05-24T21:46:27.200329Z"}}, "outputs": [], "source": ["def call_flockx_agent(prompt):\n", "    url = \"https://api.flockx.io/api/v1/agents/af1d4156-2b7b-45cc-98d2-ee8f96652c56/prompt\"\n", "    headers = {\n", "        \"Authorization\": f\"Token {FLOCKX_KEY}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    data = {\n", "        \"prompt\": prompt\n", "    }\n", "\n", "    response = requests.post(url, json=data, headers=headers)\n", "\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    else:\n", "        return {\n", "            \"error\": response.status_code,\n", "            \"message\": response.text\n", "        }"]}, {"cell_type": "code", "execution_count": 8, "id": "6376aedf", "metadata": {"ExecuteTime": {"end_time": "2025-05-24T21:46:31.884878Z", "start_time": "2025-05-24T21:46:28.728119Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'message': \"I'm <PERSON><PERSON><PERSON><PERSON><PERSON>, here to help you recognize early signs of breast cancer. I'll ask you a few brief questions about common symptoms.\\n\\nHave you noticed any new lumps or swelling in your breast or underarm?\"}\n"]}], "source": ["print(call_flockx_agent(\"I want to know if I have breast cancer\"))"]}, {"cell_type": "code", "execution_count": 9, "id": "d7df14db8b314bae", "metadata": {"ExecuteTime": {"end_time": "2025-05-24T21:50:43.546754Z", "start_time": "2025-05-24T21:50:40.871401Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'message': \"I'm <PERSON><PERSON><PERSON><PERSON><PERSON>, here to help you understand potential signs of breast cancer. I'd like to ask you a few brief questions to better assist you.\\n\\nHave you noticed any new lumps or swelling in your breast or underarm area?\"}\n"]}], "source": ["print(call_flockx_agent(\"Yes\"))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}