"""add calendar_id to doctors

Revision ID: 97d5119daa69
Revises: 
Create Date: 2025-05-24 15:27:37.098888

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '97d5119daa69'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('doctors', sa.Column('calendar_id', sa.String(), nullable=True))
    op.drop_constraint(op.f('doctors_id_fkey'), 'doctors', type_='foreignkey')
    op.create_foreign_key(None, 'doctors', 'users', ['id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(op.f('patients_id_fkey'), 'patients', type_='foreignkey')
    op.create_foreign_key(None, 'patients', 'users', ['id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'patients', type_='foreignkey')
    op.create_foreign_key(op.f('patients_id_fkey'), 'patients', 'users', ['id'], ['id'])
    op.drop_constraint(None, 'doctors', type_='foreignkey')
    op.create_foreign_key(op.f('doctors_id_fkey'), 'doctors', 'users', ['id'], ['id'])
    op.drop_column('doctors', 'calendar_id')
    # ### end Alembic commands ###
