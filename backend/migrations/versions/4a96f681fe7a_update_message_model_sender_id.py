"""update message model sender_id

Revision ID: 4a96f681fe7a
Revises: 53858ccb0cca
Create Date: 2025-05-24 19:24:30.329338

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4a96f681fe7a'
down_revision: Union[str, None] = '53858ccb0cca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('conversations', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('conversations', 'agent_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_constraint(op.f('messages_sender_id_fkey'), 'messages', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(op.f('messages_sender_id_fkey'), 'messages', 'users', ['sender_id'], ['id'], ondelete='CASCADE')
    op.alter_column('conversations', 'agent_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('conversations', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
