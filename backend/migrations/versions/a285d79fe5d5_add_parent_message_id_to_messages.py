"""add parent_message_id to messages

Revision ID: a285d79fe5d5
Revises: 4a96f681fe7a
Create Date: 2024-05-24 16:45:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a285d79fe5d5'
down_revision: Union[str, None] = '4a96f681fe7a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('parent_message_id', sa.Integer(), nullable=True))
    op.create_foreign_key(
        'fk_parent_message',
        'messages', 'messages',
        ['parent_message_id'], ['id'],
        ondelete='SET NULL'
    )
    op.drop_constraint('conversations_agent_id_fkey', 'conversations', type_='foreignkey')
    op.drop_column('conversations', 'agent_id')
    op.drop_table('virtual_assistants')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('virtual_assistants',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('model_version', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('capabilities', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('last_training_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('virtual_assistants_pkey'))
    )
    op.add_column('conversations', sa.Column('agent_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f('conversations_agent_id_fkey'), 'conversations', 'virtual_assistants', ['agent_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_parent_message', 'messages', type_='foreignkey')
    op.drop_column('messages', 'parent_message_id')
    # ### end Alembic commands ###
